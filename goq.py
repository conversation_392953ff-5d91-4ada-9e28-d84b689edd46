import time
import os
import schedule
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import NoSuchElementException, TimeoutException
from datetime import datetime, timedelta, date
import warnings
warnings.filterwarnings('ignore')

def shop1_job():
    print("开始执行店铺1的预约任务")
    try:
        # 设置下载目录
        DOWNLOAD_DIR = r'C:\Users\<USER>\Desktop\chromedriver-win64\temp2'
        os.environ['DOWNLOAD_DIR'] = DOWNLOAD_DIR
        os.chdir(DOWNLOAD_DIR)
        # Selenium的Chrome选项配置
        options = webdriver.ChromeOptions()
        service = Service(r'C:\Users\<USER>\Desktop\chromedriver-win64\chromedriver.exe')
        # options.add_argument('--headless')  # 根据需要启用无头模式
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1280,1024')
        options.add_experimental_option("prefs", {
            "profile.default_content_settings.popups": 0,
            "download_restrictions": 0,
            "download.default_directory": DOWNLOAD_DIR,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing_for_trusted_sources_enabled": False,
            "safebrowsing.enabled": True
        })
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3')

        driver = webdriver.Chrome(service=service, options=options)
        # 登录信息
        LOGIN_ID = 'aN8KvDP6'
        LOGIN_PW = 'cJ2Gh6JK'
        SEQ_ID = 'takasyou'
        SEQ_PW = 'R@n4Dm6@ss!#7'
        driver.get('https://order.goqsystem.com/goq21/form/goqsystem_new/systemlogin.php')    

        login_id1 = driver.find_element(By.NAME, "login_id")
        login_id1.send_keys(LOGIN_ID)
        login_pw = driver.find_element(By.NAME, "login_pw")
        login_pw.send_keys(LOGIN_PW)
        driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div/section/div/div[1]/div/div/form/div/div[1]/button").click()    

        seq_id_field = driver.find_element(By.NAME, "seq_id")
        seq_id_field.send_keys(SEQ_ID)
        seq_pw_field = driver.find_element(By.NAME, "seq_pw")
        seq_pw_field.send_keys(SEQ_PW)
        driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div/section/div/div[1]/div/div/form/div/div[2]/button").click()

        driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div/section/div/div[1]/div/div/form/div/button").click() 
        time.sleep(100)
        try:
            elements = driver.find_elements(By.XPATH, "/html/body/div[1]/div/section/div/div[1]/label")
            for element in elements:
                element.click()
                time.sleep(0.5)  # 保留原来的短暂等待
            driver.find_element(By.XPATH, "/html/body/div[1]/div/button").click()
        except:       
            print("继续执行")

        # 捕获主窗口句柄
        main_window = driver.current_window_handle
        driver.get("https://order.goqsystem.com/goq21/index.php?stat=12&page=1")
        try:
            elements = driver.find_elements(By.XPATH, "/html/body/div[1]/div/section/div/div[1]/label")
            for element in elements:
                element.click()
                time.sleep(0.5)  # 保留原来的短暂等待
            driver.find_element(By.XPATH, "/html/body/div[1]/div/button").click()
        except:       
            print("继续执行")
        time.sleep(100)
        # 点击日期选择按钮
        driver.find_element(By.XPATH, "/html/body/table[4]/tbody/tr/td/div/div/form/table/tbody/tr[5]/td[2]/table/tbody/tr/td[4]/div/input").click()
        
        # 获取当前日期
        target_date2 = datetime.now()
        year = str(target_date2.year)
        month = str(target_date2.month)
        day = str(target_date2.day)
        
        # 设置开始日期 (从当前日期往前推5天)
        start_date2 = target_date2 - timedelta(days=2)
        start_year2 = str(start_date2.year)
        start_month2 = str(start_date2.month)
        start_day2 = str(start_date2.day)

        WebElement_from_year = driver.find_element(By.CSS_SELECTOR, "#from_year")
        select_from_year = Select(WebElement_from_year)
        select_from_year.select_by_value(start_year2)

        WebElement_from_month = driver.find_element(By.CSS_SELECTOR, "#from_month")
        select_from_month = Select(WebElement_from_month)
        select_from_month.select_by_value(start_month2)

        WebElement_from_day = driver.find_element(By.CSS_SELECTOR, "#from_day")
        select_from_day = Select(WebElement_from_day)
        select_from_day.select_by_value(start_day2)  # 设为当前日期往前推5天

        # 设置结束日期 (到今天)
        WebElement_to_year = driver.find_element(By.CSS_SELECTOR, "#to_year")
        select_to_year = Select(WebElement_to_year)
        select_to_year.select_by_value(year)

        WebElement_to_month = driver.find_element(By.CSS_SELECTOR, "#to_month")
        select_to_month = Select(WebElement_to_month)
        select_to_month.select_by_value(month)

        WebElement_to_day = driver.find_element(By.CSS_SELECTOR, "#to_day")
        select_to_day = Select(WebElement_to_day)
        select_to_day.select_by_value(day)  # 设为今天       


        # 执行数据搜索
        driver.find_element(By.XPATH, "/html/body/table[4]/tbody/tr/td/div/div/form/table/tbody/tr[10]/td[2]/div/div[1]/input").click()
        time.sleep(50)  # 保留原有的睡眠时间
        try:
            element = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="seachselect2"]'))
            )
            driver.execute_script("arguments[0].scrollIntoView();", element) 
            driver.execute_script("arguments[0].click();", element)
        except NoSuchElementException:
            print("未找到元素")
        except TimeoutException:
            print("等待元素出现超时") 
        # time.sleep(100)  # 保留原有的睡眠时间
        WebElement_trader_s3 = driver.find_element(By.CSS_SELECTOR, "#trader_s3")
        select_trader_s3 = Select(WebElement_trader_s3)
        select_trader_s3.select_by_value("customize_csv_134")
        # time.sleep(300)  # 保留原有的睡眠时间  
        download_button = driver.find_element(By.XPATH, '/html/body/form/div[1]/div/div/table/tbody/tr/td[3]/table[4]/tbody/tr[1]/td/table[2]/tbody/tr[2]/td[4]/div/input')
        download_button.click()
        time.sleep(60)  # 等待下载完成
        driver.quit()
        print("店铺1任务执行完成")
    except Exception as e:
        print(f"店铺1任务执行出错: {str(e)}")
        try:
            driver.quit()
        except:
            pass

def shop2_job():
    print("开始执行店铺2的预约任务")
    try:
        # 设置下载目录
        DOWNLOAD_DIR = r'C:\Users\<USER>\Desktop\chromedriver-win64\temp2'
        os.environ['DOWNLOAD_DIR'] = DOWNLOAD_DIR
        os.chdir(DOWNLOAD_DIR)
        # Selenium的Chrome选项配置
        options = webdriver.ChromeOptions()
        service = Service(r'C:\Users\<USER>\Desktop\chromedriver-win64\chromedriver.exe')
        # options.add_argument('--headless')  # 根据需要启用无头模式
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1280,1024')
        options.add_experimental_option("prefs", {
            "profile.default_content_settings.popups": 0,
            "download_restrictions": 0,
            "download.default_directory": DOWNLOAD_DIR,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing_for_trusted_sources_enabled": False,
            "safebrowsing.enabled": True
        })
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3')

        driver = webdriver.Chrome(service=service, options=options)
        # 登录信息
        LOGIN_ID = 'goq02'
        LOGIN_PW = 'DPPVeBJ6'
        SEQ_ID = 'takasyou2'
        SEQ_PW = 'Gs0358797001'
        driver.get('https://order.goqsystem.com/goq21/form/goqsystem_new/systemlogin.php')    

        login_id1 = driver.find_element(By.NAME, "login_id")
        login_id1.send_keys(LOGIN_ID)
        login_pw = driver.find_element(By.NAME, "login_pw")
        login_pw.send_keys(LOGIN_PW)
        driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div/section/div/div[1]/div/div/form/div/div[1]/button").click()    

        seq_id_field = driver.find_element(By.NAME, "seq_id")
        seq_id_field.send_keys(SEQ_ID)
        seq_pw_field = driver.find_element(By.NAME, "seq_pw")
        seq_pw_field.send_keys(SEQ_PW)
        driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div/section/div/div[1]/div/div/form/div/div[2]/button").click()

        driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div/section/div/div[1]/div/div/form/div/button").click() 

        try:
            elements = driver.find_elements(By.XPATH, "/html/body/div[1]/div/section/div/div[1]/label")
            for element in elements:
                element.click()
                time.sleep(0.5)  # 保留原来的短暂等待
            driver.find_element(By.XPATH, "/html/body/div[1]/div/button").click()
        except:       
            print("继续执行")

        # 捕获主窗口句柄
        main_window = driver.current_window_handle
        driver.get("https://order.goqsystem.com/goq21/index.php?stat=12&page=1")
        try:
            elements = driver.find_elements(By.XPATH, "/html/body/div[1]/div/section/div/div[1]/label")
            for element in elements:
                element.click()
                time.sleep(0.5)  # 保留原来的短暂等待
            driver.find_element(By.XPATH, "/html/body/div[1]/div/button").click()
        except:       
            print("继续执行")

        # 点击日期选择按钮
        driver.find_element(By.XPATH, "/html/body/table[4]/tbody/tr/td/div/div/form/table/tbody/tr[5]/td[2]/table/tbody/tr/td[4]/div/input").click()
        # 获取当前日期
        target_date = datetime.now()
        year = str(target_date.year)
        month = str(target_date.month)
        day = str(target_date.day)

        # 设置开始日期 (从当月1日开始)
        WebElement_from_year = driver.find_element(By.CSS_SELECTOR, "#from_year")
        select_from_year = Select(WebElement_from_year)
        select_from_year.select_by_value(year)

        WebElement_from_month = driver.find_element(By.CSS_SELECTOR, "#from_month")
        select_from_month = Select(WebElement_from_month)
        select_from_month.select_by_value(month)

        WebElement_from_day = driver.find_element(By.CSS_SELECTOR, "#from_day")
        select_from_day = Select(WebElement_from_day)
        select_from_day.select_by_value("1")  # 设为当月1日

        # 设置结束日期 (到今天)
        WebElement_to_year = driver.find_element(By.CSS_SELECTOR, "#to_year")
        select_to_year = Select(WebElement_to_year)
        select_to_year.select_by_value(year)

        WebElement_to_month = driver.find_element(By.CSS_SELECTOR, "#to_month")
        select_to_month = Select(WebElement_to_month)
        select_to_month.select_by_value(month)

        WebElement_to_day = driver.find_element(By.CSS_SELECTOR, "#to_day")
        select_to_day = Select(WebElement_to_day)
        select_to_day.select_by_value(day)  # 设为今天
        # 执行数据搜索
        driver.find_element(By.XPATH, "/html/body/table[4]/tbody/tr/td/div/div/form/table/tbody/tr[10]/td[2]/div/div[1]/input").click()
        time.sleep(50)  # 保留原有的睡眠时间
        try:
            element = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="seachselect2"]'))
            )
            driver.execute_script("arguments[0].scrollIntoView();", element) 
            driver.execute_script("arguments[0].click();", element)
        except NoSuchElementException:
            print("未找到元素")
        except TimeoutException:
            print("等待元素出现超时") 
        time.sleep(100)  # 保留原有的睡眠时间
        WebElement_trader_s3 = driver.find_element(By.CSS_SELECTOR, "#trader_s3")
        select_trader_s3 = Select(WebElement_trader_s3)
        select_trader_s3.select_by_value("customize_csv_127")
        time.sleep(300)  # 保留原有的睡眠时间  
        download_button2 = driver.find_element(By.XPATH, '/html/body/form/div[1]/div/div/table/tbody/tr/td[3]/table[4]/tbody/tr[1]/td/table[2]/tbody/tr[2]/td[4]/div/input')
        download_button2.click()
        time.sleep(60)  # 等待下载完成
        driver.quit()
        print("店铺2任务执行完成")
    except Exception as e:
        print(f"店铺2任务执行出错: {str(e)}")
        try:
            driver.quit()
        except:
            pass

def run_all_jobs():
    print("开始执行所有预约任务")
    shop1_job()
    time.sleep(60)  # 两个任务之间等待一分钟
    shop2_job()
    print("所有预约任务执行完毕")

# 设置每天凌晨2点执行任务
schedule.every().day.at("03:00").do(run_all_jobs)
if __name__ == "__main__":
    print("脚本已启动，等待执行定时任务...")
    print("脚本已启动，将在每天凌晨3:00自动执行订单预约")
    
    # 首次运行时是否立即执行一次
    run_all_jobs()  # 如果需要立即执行一次，取消此行注释
    
    while True:
        schedule.run_pending()
        time.sleep(60)  # 每分钟检查一次是否有待执行的任务


